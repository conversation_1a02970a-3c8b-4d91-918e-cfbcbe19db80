import React, { Suspense } from 'react';
import type { Metadata } from 'next';
import Container from '@/components/atoms/Container/Container';
import BillingHistoryClient from '@/components/organisms/BillingHistoryClient';
import { getInvoices } from '@/actions/transaction.action';
import { ITransaction } from '@/types/transaction';
import {
  CreditCard,
  Download,
  Calendar,
  DollarSign,
  CheckCircle,
  AlertCircle,
  Receipt,
  TrendingUp
} from 'lucide-react';

// Page metadata
export const metadata: Metadata = {
  title: 'Billing History | EduSG',
  description: 'View your billing history and transaction details for your EduSG subscription',
};

// Loading component for Suspense fallback with improved design
function BillingHistoryLoading() {
  return (
    <Container variant="default" className="px-4 py-8">
      <div className="space-y-8">
        {/* Header skeleton with stats */}
        <div className="flex items-center justify-between">
          <div className="space-y-3">
            <div className="skeleton h-10 w-64"></div>
            <div className="skeleton h-5 w-80"></div>
          </div>
          <div className="flex gap-3">
            <div className="skeleton h-10 w-32"></div>
            <div className="skeleton h-10 w-24"></div>
          </div>
        </div>

        {/* Stats cards skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-3">
                  <div className="skeleton h-4 w-24"></div>
                  <div className="skeleton h-8 w-20"></div>
                </div>
                <div className="skeleton w-12 h-12 rounded-xl"></div>
              </div>
            </div>
          ))}
        </div>

        {/* Filters skeleton */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="skeleton h-10 w-64"></div>
            <div className="skeleton h-10 w-48"></div>
            <div className="skeleton h-10 w-32"></div>
          </div>
        </div>

        {/* Table skeleton */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="p-6 border-b border-gray-100">
            <div className="skeleton h-6 w-48"></div>
          </div>
          <div className="divide-y divide-gray-100">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="p-6 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="skeleton w-12 h-12 rounded-xl"></div>
                  <div className="space-y-2">
                    <div className="skeleton h-5 w-48"></div>
                    <div className="skeleton h-4 w-32"></div>
                  </div>
                </div>
                <div className="text-right space-y-2">
                  <div className="skeleton h-5 w-20"></div>
                  <div className="skeleton h-4 w-16"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Container>
  );
}

// Main billing history content component
async function BillingHistoryContent() {
  const invoiceResult = await getInvoices();

  let transactions: ITransaction[] = [];
  if (invoiceResult.success && Array.isArray(invoiceResult.data)) {
    // Map the raw invoice data from Stripe to our ITransaction format
    console.log('invoiceResult', invoiceResult.data);
    transactions = invoiceResult.data.map((invoice: any) => ({
      id: invoice.id,
      date: new Date(invoice.created * 1000).toISOString(),
      description: `Invoice #${invoice.number}`,
      amount: invoice.amount_paid,
      currency: invoice.currency.toUpperCase(),
      status: invoice.status,
      invoiceUrl: invoice.hosted_invoice_url,
      receiptUrl: invoice.invoice_pdf,
      period_end: invoice.period_end,
      period_start: invoice.period_start,
      paymentMethod: invoice.payment_settings?.payment_method_types[0] ?? 'N/A'
    }));
  } else {
    // Handle the case where fetching invoices fails
    // You might want to show an error message to the user
    console.error('Failed to fetch invoices:', invoiceResult.message);
  }

  const latestInvoice = transactions[0];
  let nextInvoiceDate = 'N/A';
  if (latestInvoice && latestInvoice.period_end) {
    const periodEndDate = new Date(latestInvoice.period_end * 1000);
    periodEndDate.setDate(periodEndDate.getDate() + 30);
    nextInvoiceDate = periodEndDate.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }

  // Calculate stats based on fetched data
  const totalSpent = transactions
    .filter(item => item.status === 'paid')
    .reduce((sum, item) => sum + item.amount, 0);
  
  const thisMonthSpent = transactions
    .filter(item => {
      const itemDate = new Date(item.date);
      const now = new Date();
      return item.status === 'paid' && 
             itemDate.getMonth() === now.getMonth() && 
             itemDate.getFullYear() === now.getFullYear();
    })
    .reduce((sum, item) => sum + item.amount, 0);

  return (
    <Container variant="default" className="px-4 py-8">
      <div className="space-y-8">
        {/* Header Section */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <div className="space-y-2">
            <h1 className="text-3xl md:text-4xl font-bold text-text-primary flex items-center gap-3">
              <div className="w-12 h-12 bg-primary-action/10 rounded-xl flex items-center justify-center">
                <CreditCard className="w-6 h-6 text-primary-action" />
              </div>
              Billing History
            </h1>
            <p className="text-text-secondary text-lg">
              Track your payments, download invoices, and manage your billing preferences
            </p>
          </div>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Total Spent */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary font-medium">Total Spent</p>
                <p className="text-3xl font-bold text-text-primary mt-1">
                  {new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                  }).format(totalSpent / 100)}
                </p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </div>
          {/* This Month */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary font-medium">This Month</p>
                <p className="text-3xl font-bold text-text-primary mt-1">
                  {new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                  }).format(thisMonthSpent / 100)}
                </p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                <Calendar className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </div>
          {/* Next Invoice */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary font-medium">Next Invoice</p>
                <p className="text-3xl font-bold text-text-primary mt-1">{nextInvoiceDate}</p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </div>
        </div>
        
        <BillingHistoryClient initialTransactions={transactions} />
      </div>
    </Container>
  );
}

// Main page component
export default function BillingHistoryPage() {
  return (
    <Suspense fallback={<BillingHistoryLoading />}>
      <BillingHistoryContent />
    </Suspense>
  );
} 