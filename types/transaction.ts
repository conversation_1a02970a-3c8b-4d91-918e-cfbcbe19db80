// ============================================================================
// TRANSACTION TYPESCRIPT INTERFACES
// ============================================================================

export interface ITransaction {
  id: string;
  date: string; // ISO date string
  description: string;
  amount: number; // in cents
  currency: string;
  status: 'paid' | 'failed' | 'pending' | 'open' | 'void' | 'uncollectible';
  paymentMethod?: string;
  invoiceUrl: string;
  receiptUrl: string;
  type?: string; // additional field for transaction type
  period_start?: number; // UNIX timestamp
  period_end?: number; // UNIX timestamp
}

export interface ITransactionFilters {
  startDate?: string; // ISO date string
  endDate?: string; // ISO date string
  status?: 'paid' | 'failed' | 'pending' | 'all';
  page?: number;
  limit?: number;
  sortBy?: 'date' | 'amount' | 'status';
  sortOrder?: 'asc' | 'desc';
}

export interface ITransactionPagination {
  items: ITransaction[];
  currentPage: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
}

export interface ITransactionActionResult {
  success: boolean;
  data?: ITransactionPagination | ITransaction | ITransaction[];
  message?: string;
  errors?: Record<string, string[]>;
}
