export enum EViewType {
  LISTING = 'listing',
  CREATE = 'create',
  REVIEW = 'review',
}

export enum ERoutes {
  HOME = '/',
  MANAGE_WORKSHEET = '/manage-worksheet',
  <PERSON><PERSON><PERSON>_WORKSHEET_CREATE = `/manage-worksheet?type=${EViewType.CREATE}`,
  MANAGE_WORKSHEET_REVIEW = `/manage-worksheet?type=${EViewType.REVIEW}`,
  USERS_MANAGEMENT = '/users-management',
  TEACHER_MANAGEMENT = '/teacher-management',
  SCHOOLS = '/school-management',
  PACKAGES = '/admin/packages',
  MY_SCHOOL = '/my-school',
  CREATE_SCHOOL = '/create-school',
  ONBOARDING_START = '/onboarding/school-details',
  SCHOOL_CUSTOMIZATION = '/school-customization',
  BILLING_HISTORY = '/billing/history',
  MY_SUBSCRIPTION = '/subscription/me',
}
