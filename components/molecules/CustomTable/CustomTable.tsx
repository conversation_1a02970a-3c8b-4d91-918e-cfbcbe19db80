"use client"
import {
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  RowSelectionState,
  Updater,
  useReactTable,
  PaginationState, // Added for manual pagination state
} from '@tanstack/react-table';
import {useEffect, useState} from 'react'; // Added useEffect


import {cn} from '@/utils/cn';

import { ButtonProps } from '@/components/atoms/Button/Button';
import Table from '@/components/atoms/Table';
import TBody from '@/components/atoms/Table/TBody';
import Td from '@/components/atoms/Table/Td';
import Th from '@/components/atoms/Table/Th';
import THead from '@/components/atoms/Table/THead';
import Tr from '@/components/atoms/Table/Tr';
import { Ellipsis } from 'lucide-react';
import { TTableCommonProps } from './CustomTable.types';

export const SELECTION_LENGTH_REPLACEMENT = '{selectionLength}';

export default function TableCommon<T>({
  columns,
  tableData, // Represents items for the current page in manual pagination
  isLoading = false,
  wrapperClass = '',
  // Props for manual pagination
  manualPagination = true, // Default to true as per existing code
  pageCount = -1, // Total number of pages, -1 if not applicable or server-side unknown
  pagination, // Current pagination state (pageIndex, pageSize)
  onPaginationChange, // Callback for when pagination state changes
  sortingState,
  onSortingChange,
  theadClass = '',
  tbodyClass = '',
  tableHeight = 'h-full',
  handleClickTableRow,
  selectionButtonProps,
  renderRowClass,
}: TTableCommonProps<T & { theadColClass?: string }> & {
  selectionButtonProps?: Partial<ButtonProps>;
  renderRowClass?: (row: T) => string;
  // Adding pagination props to the main type might be cleaner, but for now, let's add here
  manualPagination?: boolean;
  pageCount?: number;
  pagination?: PaginationState;
  onPaginationChange?: (updater: Updater<PaginationState>) => void;
  sortingState?: any;
  onSortingChange?: (updater: Updater<any>) => void;
}) {
  const [rowSelection, setRowSelection] = useState({});
  // Internal pagination state if not controlled from outside
  const [_pagination, _setPagination] = useState<PaginationState>({
    pageIndex: 0, // tanstack-table uses 0-based index
    pageSize: 10, // Default page size
     ...pagination, // Override with prop if provided
  });

  // If pagination is controlled from outside, use that.
  const effectivePagination = pagination !== undefined ? pagination : _pagination;
  const effectiveSetPagination = onPaginationChange !== undefined ? onPaginationChange : _setPagination;

  useEffect(() => {
    if (pagination) {
      _setPagination(prev => ({...prev, ...pagination}));
    }
  }, [pagination]);


  const handleRowSelection = (rowSelectFnc: Updater<RowSelectionState>) => {
    if (typeof rowSelectFnc !== 'function') return;
    const newSortState = rowSelectFnc(rowSelection);
    setRowSelection(newSortState);
  };

  const table = useReactTable({
    data: tableData, // Should be the data for the current page
    columns: columns,
    state: {
      rowSelection,
      pagination: effectivePagination,
      sorting: sortingState,
    },
    enableColumnResizing: true,
    manualSorting: true, // Assuming sorting is also manual if pagination is
    manualPagination: manualPagination, // Controlled by this prop
    columnResizeMode: 'onChange',
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: manualPagination ? undefined : getPaginationRowModel(), // Only if not manual
    enableRowSelection: true,
    onRowSelectionChange: handleRowSelection,
    onSortingChange,
    onPaginationChange: manualPagination ? effectiveSetPagination : undefined,
    ...(manualPagination && pageCount !== -1 && { pageCount: pageCount }), // Set pageCount if manual and provided
    // rowCount: manualPagination ? totalRowCount : undefined, // If you have total items count
    getRowId: (row: any) => String(row?.id || Math.random()),
  });
  const selectionRowIds = Object.keys(rowSelection) || [];
  const isDisabledSelection = !selectionRowIds?.length;

  const tableRows = table?.getRowModel().rows;
  const isNodata = !isLoading && !tableRows?.length;
  const isShowSelectionButton =
    !isLoading && !!selectionButtonProps && !isNodata;
  return (
    <>
      <Table
        wrapperClass={cn(
          'scrollbar overflow-y-auto border border-gray-200 rounded-[0.5rem]',
          wrapperClass,
          {
            'overflow-y-hidden': isNodata || isLoading,
            [tableHeight]: !(isNodata || isLoading),
          }
        )}
      >
        <THead
          className={cn(
            'sticky top-0 z-10 overflow-y-hidden bg-dark-gray-300',
            theadClass
          )}
        >
          {table.getHeaderGroups().map((headerGroup) => (
            <Tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <Th
                  key={header.id}
                  colSpan={header.colSpan}
                  className={
                    (header.column.columnDef as { theadColClass?: string })
                      ?.theadColClass || ''
                  }
                >
                  {header.isPlaceholder ? null : (
                    <div
                      className={cn(
                        'flex gap-2 text-nowrap',
                        header.column.getCanSort()
                          ? 'text-dark-gray-400 cursor-pointer select-none'
                          : 'text-dark-gray-400'
                      )}
                      onClick={header.column.getToggleSortingHandler()}
                    >
                      <div>
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                      </div>
                      {/* {header.column.getCanSort() && (
                        <Sorter sort={header.column.getIsSorted()} />
                      )} */}
                    </div>
                  )}
                </Th>
              ))}
            </Tr>
          ))}
        </THead>
        <TBody
          className={cn('z-1 sticky top-10 overflow-y-hidden', tbodyClass)}
        >
          {!isLoading &&
            tableRows?.map((row) => {
              return (
                <Tr
                  key={row.id}
                  className={renderRowClass ? renderRowClass(row.original) : ''}
                  onClick={() => handleClickTableRow?.(row)}
                >
                  {row.getVisibleCells().map((cell) => {
                    return (
                      <Td key={cell.id} className="text-dark-gray">
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </Td>
                    );
                  })}
                </Tr>
              );
            })}
        </TBody>
      </Table>
      <Table
        wrapperClass={cn('relative h-full overflow-y-hidden', wrapperClass, {
          [tableHeight]: isLoading,
          hidden: !(isLoading || isNodata),
        })}
      >
        <TBody className="h-full">
          <Tr>
            <Td>
              {isLoading ? (
                <Ellipsis />
              ) : (
                <div className="text-body14">
                  Ooops! Looks like there is no data
                </div>
              )}
            </Td>
          </Tr>
        </TBody>
      </Table>
    </>
  );
}
