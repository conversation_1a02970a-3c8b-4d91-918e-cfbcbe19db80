'use client';

import React from 'react';
import { ColumnDef, Row } from '@tanstack/react-table';
import { cn } from '@/utils/cn';
import { ChevronDown, ChevronRight } from 'lucide-react';

interface ResponsiveTableProps<T> {
  columns: ColumnDef<T, any>[];
  data: T[];
  onRowClick?: (row: Row<T>) => void;
  className?: string;
  mobileBreakpoint?: 'sm' | 'md' | 'lg';
  priorityColumns?: string[]; // Column keys to show on mobile
  expandableContent?: (row: T) => React.ReactNode;
}

export default function ResponsiveTable<T>({
  columns,
  data,
  onRowClick,
  className,
  mobileBreakpoint = 'md',
  priorityColumns = [],
  expandableContent,
}: ResponsiveTableProps<T>) {
  const [expandedRows, setExpandedRows] = React.useState<Set<number>>(new Set());

  const toggleRowExpansion = (index: number) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedRows(newExpanded);
  };

  // Get priority columns for mobile
  const mobileColumns = columns.filter(col => 
    priorityColumns.includes((col as any).accessorKey || col.id || '')
  );

  // Get secondary columns for expansion
  const secondaryColumns = columns.filter(col => 
    !priorityColumns.includes((col as any).accessorKey || col.id || '')
  );

  const renderMobileCard = (item: T, index: number) => {
    const isExpanded = expandedRows.has(index);
    const hasExpandableContent = expandableContent || secondaryColumns.length > 0;

    return (
      <div
        key={index}
        className="bg-white border border-gray-200 rounded-lg p-4 mb-3 shadow-sm"
      >
        {/* Primary content - always visible */}
        <div className="space-y-3">
          {mobileColumns.map((column, colIndex) => {
            const value = (column as any).cell 
              ? (column as any).cell({ row: { original: item }, getValue: () => (item as any)[(column as any).accessorKey] })
              : (item as any)[(column as any).accessorKey];
            
            return (
              <div key={colIndex} className="flex justify-between items-start">
                <span className="text-sm font-medium text-gray-600 min-w-0 flex-shrink-0 mr-3">
                  {typeof column.header === 'string' ? column.header : 'Field'}:
                </span>
                <div className="text-sm text-gray-900 text-right min-w-0 flex-1">
                  {value}
                </div>
              </div>
            );
          })}
        </div>

        {/* Expandable toggle */}
        {hasExpandableContent && (
          <button
            onClick={() => toggleRowExpansion(index)}
            className="mt-3 flex items-center text-blue-600 text-sm font-medium hover:text-blue-800 transition-colors"
          >
            {isExpanded ? (
              <>
                <ChevronDown className="w-4 h-4 mr-1" />
                Show Less
              </>
            ) : (
              <>
                <ChevronRight className="w-4 h-4 mr-1" />
                Show More
              </>
            )}
          </button>
        )}

        {/* Expandable content */}
        {isExpanded && (
          <div className="mt-4 pt-4 border-t border-gray-100 space-y-3">
            {/* Secondary columns */}
            {secondaryColumns.map((column, colIndex) => {
              const value = (column as any).cell 
                ? (column as any).cell({ row: { original: item }, getValue: () => (item as any)[(column as any).accessorKey] })
                : (item as any)[(column as any).accessorKey];
              
              return (
                <div key={colIndex} className="flex justify-between items-start">
                  <span className="text-sm font-medium text-gray-600 min-w-0 flex-shrink-0 mr-3">
                    {typeof column.header === 'string' ? column.header : 'Field'}:
                  </span>
                  <div className="text-sm text-gray-900 text-right min-w-0 flex-1">
                    {value}
                  </div>
                </div>
              );
            })}
            
            {/* Custom expandable content */}
            {expandableContent && (
              <div className="mt-3">
                {expandableContent(item)}
              </div>
            )}
          </div>
        )}

        {/* Row click handler */}
        {onRowClick && (
          <button
            onClick={() => onRowClick({ original: item } as Row<T>)}
            className="mt-3 w-full text-center py-2 text-blue-600 text-sm font-medium hover:bg-blue-50 rounded transition-colors"
          >
            View Details
          </button>
        )}
      </div>
    );
  };

  const renderDesktopTable = () => (
    <div className="overflow-x-auto">
      <table className="table table-hover w-full">
        <thead className="bg-white border-b">
          <tr>
            {columns.map((column, index) => (
              <th key={index} className="px-4 py-3 text-left text-sm font-semibold text-gray-900">
                {typeof column.header === 'string' ? column.header : 'Column'}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-100">
          {data.map((item, index) => (
            <tr
              key={index}
              className={cn(
                'hover:bg-gray-50 transition-colors',
                onRowClick && 'cursor-pointer'
              )}
              onClick={() => onRowClick?.({ original: item } as Row<T>)}
            >
              {columns.map((column, colIndex) => {
                const value = (column as any).cell 
                  ? (column as any).cell({ row: { original: item }, getValue: () => (item as any)[(column as any).accessorKey] })
                  : (item as any)[(column as any).accessorKey];
                
                return (
                  <td key={colIndex} className="px-4 py-3 text-sm text-gray-900">
                    {value}
                  </td>
                );
              })}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  return (
    <div className={cn('w-full', className)}>
      {/* Mobile view - Card layout */}
      <div className={cn(`block ${mobileBreakpoint}:hidden`)}>
        {data.length > 0 ? (
          <div className="bg-gray-50 -mx-4 px-4 py-1">
            <div className="space-y-3">
              {data.map((item, index) => renderMobileCard(item, index))}
            </div>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            No data available
          </div>
        )}
      </div>

      {/* Desktop view - Table layout */}
      <div className={cn(`hidden ${mobileBreakpoint}:block`)}>
        {renderDesktopTable()}
      </div>
    </div>
  );
}
