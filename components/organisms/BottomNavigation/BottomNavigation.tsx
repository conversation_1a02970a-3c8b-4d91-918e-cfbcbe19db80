'use client';

import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { cn } from '@/utils/cn';
import { HomeIcon, ClipboardListIcon, Building2Icon, UserIcon, MenuIcon, ArrowLeft } from 'lucide-react';
import type { ComponentProps } from 'react';

export interface BottomNavigationItem {
  label: string;
  href: string;
  icon: React.ReactNode;
}

export interface BottomNavigationProps extends ComponentProps<'div'> {
  items: BottomNavigationItem[];
}

const BottomNavigation = ({ items, className, ...props }: BottomNavigationProps) => {
  const pathname = usePathname();
  const router = useRouter();

  // Check if we should show back button
  const showBackButton = pathname?.includes('/manage-worksheet') && pathname?.includes('type=create');

  // Get the most important items for bottom navigation (max 4-5 items)
  const getBottomNavItems = () => {
    const bottomNavItems: BottomNavigationItem[] = [];

    // Add back button if on create worksheet page
    if (showBackButton) {
      bottomNavItems.push({
        label: 'Back',
        href: '#',
        icon: <ArrowLeft className="w-5 h-5" />
      });
    }

    // Always add menu toggle
    bottomNavItems.push({
      label: 'Menu',
      href: '#',
      icon: <MenuIcon className="w-5 h-5" />
    });

    // Add remaining navigation items (adjust count based on back button)
    const maxItems = showBackButton ? 2 : 3;
    const importantItems = items.slice(0, maxItems);
    
    // Simplify labels for mobile
    const simplifiedItems = importantItems.map(item => ({
      ...item,
      label: item.label.replace('My ', '').split(' ')[0] // Simplify labels by removing 'My' and taking first word
    }));
    
    bottomNavItems.push(...simplifiedItems);

    return bottomNavItems;
  };

  const bottomNavItems = getBottomNavItems();

  if (items.length === 0) return null;

  return (
    <div
      className={cn(
        'dock dock-sm fixed bottom-0 left-0 right-0 z-50 bg-base-100/95 backdrop-blur-sm border-t border-base-300 lg:hidden',
        'safe-area-inset-bottom grid grid-flow-col auto-cols-fr', // Use grid for equal width distribution
        className
      )}
      style={{ height: '64px' }} // Fixed height
      {...props}
    >
      {bottomNavItems.map((item, index) => {
        const isActive = pathname === item.href;
        const isMenuButton = item.label === 'Menu';
        const isBackButton = item.label === 'Back';

        const baseItemClasses = cn(
          'dock-item flex flex-col items-center justify-center transition-all duration-200',
          'relative min-h-8', // Consistent height
          'hover:bg-base-200 active:bg-base-300',
          isActive && 'text-primary'
        );

        const iconClasses = cn(
          'flex-shrink-0 mb-1 transition-transform duration-200',
          isActive ? 'scale-110 text-primary' : 'text-base-content/70 group-hover:scale-105'
        );

        const labelClasses = cn(
          'text-xs font-medium truncate px-1', // Prevent text wrapping
          isActive ? 'text-primary' : 'text-base-content/70'
        );

        // Active indicator line
        const activeIndicator = isActive && (
          <div className="absolute bottom-0 left-1/2 -translate-x-1/2 w-12 h-0.5 bg-primary rounded-t-full" />
        );

        if (isMenuButton) {
          return (
            <label
              key={index}
              htmlFor="dashboard-drawer"
              className={baseItemClasses}
              aria-label="Toggle menu"
              role="button"
              tabIndex={0}
            >
              <span className={iconClasses}>{item.icon}</span>
              <span className={labelClasses}>{item.label}</span>
            </label>
          );
        }

        if (isBackButton) {
          return (
            <button
              key={index}
              onClick={() => router.push('/manage-worksheet')}
              className={baseItemClasses}
              aria-label="Go back to worksheets"
            >
              <span className={iconClasses}>{item.icon}</span>
              <span className={labelClasses}>{item.label}</span>
            </button>
          );
        }

        return (
          <Link
            key={index}
            href={item.href}
            className={baseItemClasses}
            aria-current={isActive ? 'page' : undefined}
            aria-label={`${item.label} ${isActive ? '(current page)' : ''}`}
          >
            <span className={iconClasses}>{item.icon}</span>
            <span className={labelClasses}>{item.label}</span>
            {activeIndicator}
          </Link>
        );
      })}
    </div>
  );
};

export default BottomNavigation;
