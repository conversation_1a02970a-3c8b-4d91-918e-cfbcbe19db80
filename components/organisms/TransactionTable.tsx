"use client";

import React, { useMemo } from 'react';
import { transactionColumns } from './TransactionTableConfig';
import { ITransaction } from '@/types/transaction';
import ResponsiveTable from '@/components/molecules/ResponsiveTable/ResponsiveTable';
import TableSkeleton from '@/components/molecules/CustomTable/TableSkeleton';
import { SortingState, Updater } from '@tanstack/react-table';

interface TransactionTableProps {
  transactions: ITransaction[];
  isLoading?: boolean;
  sortingState?: SortingState;
  onSortingChange?: (updater: Updater<SortingState>) => void;
}

export default function TransactionTable({
  transactions,
  isLoading = false,
}: TransactionTableProps) {
  const columns = useMemo(() => transactionColumns, []);
  // Define which columns are essential for mobile view
  const priorityColumns = useMemo(
    () => ['date', 'description', 'amount', 'status', 'actions'],
    []
  );

  if (isLoading && !transactions.length) {
    return <TableSkeleton rows={8} />;
  }

  return (
    <ResponsiveTable<ITransaction>
      columns={columns}
      data={transactions}
      priorityColumns={priorityColumns}
      mobileBreakpoint="md"
    />
  );
} 