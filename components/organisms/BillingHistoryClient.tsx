'use client';

import React, { useState, useTransition } from 'react';
import { ITransaction } from '@/types/transaction';
import TransactionTable from './TransactionTable';
import { SortingState } from '@tanstack/react-table';

interface BillingHistoryClientProps {
  initialTransactions: ITransaction[];
}

export default function BillingHistoryClient({
  initialTransactions
}: BillingHistoryClientProps) {
  const [transactions] = useState<ITransaction[]>(initialTransactions);
  const [isPending] = useTransition();
  const [sorting, setSorting] = useState<SortingState>([]);

  const handleSortingChange: (updater: SortingState | ((prev: SortingState) => SortingState)) => void = (updater) => {
    const newState = typeof updater === 'function' ? updater(sorting) : updater;
    setSorting(newState);
  };

  return (
    <>
      {/* Transactions Table */}
      <TransactionTable
        transactions={transactions}
        isLoading={isPending}
        sortingState={sorting}
        onSortingChange={handleSortingChange}
      />
    </>
  );
}