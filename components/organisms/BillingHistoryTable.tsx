'use client';

import React from 'react';
import { ITransaction } from '@/types/transaction';
import {
  Clock,
  Download,
  CheckCircle,
  XCircle,
  AlertCircle,
  MoreHorizontal,
  Receipt,
  FileText,
  CreditCard
} from 'lucide-react';

interface BillingHistoryTableProps {
  transactions: ITransaction[];
  isPending: boolean;
}

// Status component
function StatusBadge({ status }: { status: string }) {
  const statusConfig = {
    paid: {
      icon: CheckCircle,
      className: 'bg-green-50 text-green-700 border-green-200',
      text: 'Paid'
    },
    failed: {
      icon: XCircle,
      className: 'bg-red-50 text-red-700 border-red-200',
      text: 'Failed'
    },
    pending: {
      icon: AlertCircle,
      className: 'bg-yellow-50 text-yellow-700 border-yellow-200',
      text: 'Pending'
    }
  };

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
  const Icon = config.icon;

  return (
    <span className={`inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium border ${config.className}`}>
      <Icon className="w-3 h-3" />
      {config.text}
    </span>
  );
}

export default function BillingHistoryTable({ transactions, isPending }: BillingHistoryTableProps) {
  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount / 100);
  };

  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(new Date(dateString));
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      <div className="p-6 border-b border-gray-100">
        <h2 className="text-xl font-semibold text-text-primary flex items-center gap-2">
          <FileText className="w-5 h-5" />
          Transaction History
          {isPending && (
            <span className="loading loading-spinner loading-sm text-primary-action"></span>
          )}
        </h2>
      </div>

      {transactions.length === 0 ? (
        <div className="text-center py-16">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Clock className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-text-primary mb-2">No transactions found</h3>
          <p className="text-text-secondary mb-6">
            {isPending 
              ? "Loading transactions..." 
              : "No transactions match your current filters."
            }
          </p>
          <button className="btn btn-primary gap-2">
            <CreditCard className="w-4 h-4" />
            View Pricing Plans
          </button>
        </div>
      ) : (
        <div className="divide-y divide-gray-100">
          {transactions.map((transaction) => (
            <div key={transaction.id} className="p-4 md:p-6 hover:bg-gray-50 transition-colors duration-200">
              <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                {/* Left side - Transaction info */}
                <div className="flex items-center space-x-4 flex-1">
                  <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                    transaction.status === 'paid'
                      ? 'bg-green-50'
                      : transaction.status === 'failed'
                      ? 'bg-red-50'
                      : 'bg-yellow-50'
                  }`}>
                    <Receipt className={`w-6 h-6 ${
                      transaction.status === 'paid'
                        ? 'text-green-600'
                        : transaction.status === 'failed'
                        ? 'text-red-600'
                        : 'text-yellow-600'
                    }`} />
                  </div>

                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-text-primary truncate">
                      {transaction.description}
                    </h3>
                    <div className="flex items-center flex-wrap gap-x-4 gap-y-1 mt-1">
                      <p className="text-sm text-text-secondary">
                        {formatDate(transaction.date)}
                      </p>
                      <p className="text-sm text-text-secondary">
                        {transaction.paymentMethod}
                      </p>
                      <StatusBadge status={transaction.status} />
                    </div>
                  </div>
                </div>

                {/* Right side - Amount and actions */}
                <div className="flex items-center justify-between md:justify-end md:gap-4 w-full md:w-auto">
                  <div className="text-left md:text-right">
                    <p className={`font-semibold ${
                      transaction.status === 'paid'
                        ? 'text-text-primary'
                        : 'text-text-secondary'
                    }`}>
                      {formatAmount(transaction.amount, transaction.currency)}
                    </p>
                    <p className="text-sm text-text-secondary">
                      ID: {transaction.id}
                    </p>
                  </div>

                  {/* Action menu */}
                  <div className="dropdown dropdown-end">
                    <button tabIndex={0} className="btn btn-ghost btn-sm btn-circle">
                      <MoreHorizontal className="w-4 h-4" />
                    </button>
                    <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                      <li>
                        <a href={transaction.invoiceUrl} target="_blank" rel="noopener noreferrer" className="gap-2">
                          <Download className="w-4 h-4" />
                          Download Invoice
                        </a>
                      </li>
                      <li>
                        <a href={transaction.receiptUrl} target="_blank" rel="noopener noreferrer" className="gap-2">
                          <Receipt className="w-4 h-4" />
                          View Receipt
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
